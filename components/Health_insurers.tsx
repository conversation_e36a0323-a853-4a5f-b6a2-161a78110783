import { <PERSON>oleanField, DataTable, DateField, List } from 'react-admin';

export const Health_insurerList = () => (
    <List>
        <DataTable>
            <DataTable.NumberCol source="claim_settlement_ratio" />
            <DataTable.Col source="created_at">
                <DateField source="created_at" />
            </DataTable.Col>
            <DataTable.Col source="deleted_at" />
            <DataTable.Col source="id" />
            <DataTable.Col source="logo_url" />
            <DataTable.Col source="name" />
            <DataTable.NumberCol source="network_hospital_count" />
            <DataTable.Col source="network_hospital_url" />
            <DataTable.Col source="preferred">
                <BooleanField source="preferred" />
            </DataTable.Col>
            <DataTable.Col source="slug" />
            <DataTable.Col source="static_content" />
            <DataTable.Col source="updated_at">
                <DateField source="updated_at" />
            </DataTable.Col>
        </DataTable>
    </List>
);