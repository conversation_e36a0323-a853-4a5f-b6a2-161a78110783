import { ArrayInput, BooleanField, CreateButton, Datagrid, DataTable, DateField, Edit, EditButton, FormTab, List, ListButton, ReferenceArrayInput, ReferenceField, ReferenceInput, ReferenceManyField, SelectInput, SimpleForm, SimpleFormIterator, TextField, TextInput, TopToolbar } from 'react-admin';


const VariantEditActions = () => {
    return (<TopToolbar>
        <ListButton resource="health_product_variants" label="Back" />
    </TopToolbar>)
}

export const Health_product_variantEdit = () => (
    <Edit actions={<VariantEditActions></VariantEditActions>}>
        <SimpleForm>
            <TextInput source="active" disabled style={{ display: 'none' }} />
            <TextInput source="id" disabled style={{ display: 'none' }} />
            <TextInput source="id" disabled style={{ display: 'none' }} />
            <ReferenceInput source='product_id' reference="health_products" label="Product">
                <SelectInput optionText={"name"} />
            </ReferenceInput>
            <TextInput source="variant_name" />
            <TextInput source="variant_slug" />

            <ArrayInput source='health_variant_faqs'>
                <SimpleFormIterator>
                    <TextInput source="id" disabled style={{ display: 'none' }} />
                    <TextInput source="created_at" disabled style={{ display: 'none' }} />
                    <TextInput source="updated_at" disabled style={{ display: 'none' }} />
                    <TextInput source='question'></TextInput>
                    <TextInput source='answer'></TextInput>
                </SimpleFormIterator>
            </ArrayInput>

        </SimpleForm>
    </Edit>
);


export const Health_product_variantList = () => (
    <List>
        <DataTable>
            <DataTable.Col source="product_id">
                <ReferenceField source="product_id" reference="health_products" />
            </DataTable.Col>
            <DataTable.Col source="variant_name" />
            <DataTable.Col source="variant_slug" />
        </DataTable>
    </List>
);