import {useState, useEffect} from "react";
import { Admin, Resource, ListGuesser, DataProvider } from 'react-admin';
import buildHasuraProvider from 'ra-data-hasura';
import { ApolloClient, InMemoryCache } from '@apollo/client';
import { Health_productCreate, Health_productEdit, Health_productList } from "../Health_products";
import { Health_product_variantEdit, Health_product_variantList } from "../Health_productVarients";
import { Health_insurerList } from "../Health_insurers";
import { Site_health_variant_static_contentList, Site_healthVariantStaticContentEdit } from "../Site_healthVariantStaticContent";

const ReactAdmin = () => {
  const [dataProvider, setDataProvider] = useState<DataProvider | null>(null);

  useEffect( () => {
    const buildDataProvider = async () => {
      const dataProvider = await buildHasuraProvider({
        client: new ApolloClient({
          uri: "http://localhost:8080/v1/graphql",
          cache: new InMemoryCache(),
          headers: {
              'x-hasura-admin-secret': "Oq25VKVadDw2p6C",
          },
        })
      })
      setDataProvider(() => dataProvider);
    };
    buildDataProvider();
  }, []);

  if (!dataProvider) return <p>Loading...</p>;

  return (
    <Admin dataProvider={dataProvider} title="My Admin">
      <Resource name="health_products" list={Health_productList} edit={Health_productEdit} create={Health_productCreate}/>
      {/* <Resource name="health_insurers" list={Health_insurerList}></Resource> */}
      <Resource name="health_product_variants" list={Health_product_variantList} edit={Health_product_variantEdit}></Resource>
      {/* <Resource name="site_health_variant_static_content" list={Site_health_variant_static_contentList} edit={Site_healthVariantStaticContentEdit}></Resource> */}
    </Admin>
  )
}

export default ReactAdmin;
